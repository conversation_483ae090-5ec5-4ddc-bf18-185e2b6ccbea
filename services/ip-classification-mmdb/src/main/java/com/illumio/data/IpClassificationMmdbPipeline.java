package com.illumio.data;

import com.illumio.data.components.IpClassificationFlowWriter;
import com.illumio.data.components.IpClassificationLookup;
import com.illumio.data.components.ResourceIdFlowReader;
import com.illumio.data.configuration.IpClassificationMmdbConfig;
import com.illumio.data.util.FlowDataValidator;
import com.illumio.data.util.MetricsUtil;
import io.opentelemetry.api.common.AttributeKey;
import io.opentelemetry.api.common.Attributes;

import lombok.extern.slf4j.Slf4j;

import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.RetriableCommitFailedException;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.common.errors.TimeoutException;
import org.apache.kafka.common.errors.DisconnectException;
import org.apache.kafka.common.errors.NetworkException;
import org.apache.kafka.common.errors.RetriableException;
import org.springframework.stereotype.Component;

import reactor.core.Disposable;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Scheduler;
import reactor.kafka.receiver.KafkaReceiver;
import reactor.kafka.receiver.ReceiverOptions;
import reactor.kafka.sender.KafkaSender;
import reactor.kafka.sender.SenderRecord;
import reactor.kafka.sender.SenderResult;
import reactor.util.retry.Retry;

import java.time.Duration;
import java.util.List;
import java.util.NoSuchElementException;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Supplier;

@Slf4j
@Component
public class IpClassificationMmdbPipeline {
    private final Supplier<KafkaReceiver<String, String>> kafkaReceiverFactory;
    private final ResourceIdFlowReader resourceIdFlowReader;
    private final IpClassificationLookup ipClassificationLookup;
    private final IpClassificationFlowWriter ipClassificationFlowWriter;
    private final String sinkTopic;
    private final IpClassificationMmdbConfig ipClassificationMmdbConfig;
    private final Scheduler ipClassificationScheduler;
    private final List<KafkaSender<String, String>> kafkaSenders;
    private final MetricsUtil metricsUtil;
    private final AtomicInteger currentSenderIndex = new AtomicInteger(0);

    private final AtomicReference<KafkaReceiver<String, String>> currentKafkaReceiver = new AtomicReference<>();
    private volatile Disposable disposable;
    private volatile Disposable networkMonitorDisposable;
    private final AtomicReference<Long> lastMessageTime = new AtomicReference<>(System.currentTimeMillis());
    private final AtomicReference<Long> lastNetworkErrorTime = new AtomicReference<>(0L);
    private final AtomicReference<Boolean> isRecreating = new AtomicReference<>(false);

    public IpClassificationMmdbPipeline(
            KafkaReceiver<String, String> kafkaReceiver,
            List<KafkaSender<String, String>> kafkaSenders,
            ResourceIdFlowReader resourceIdFlowReader,
            IpClassificationLookup ipClassificationLookup,
            IpClassificationFlowWriter ipClassificationFlowWriter,
            IpClassificationMmdbConfig ipClassificationMmdbConfig,
            Scheduler ipClassificationScheduler,
            MetricsUtil metricsUtil) {
        // Create a factory to recreate receivers when needed
        this.kafkaReceiverFactory = () -> createKafkaReceiver(ipClassificationMmdbConfig);
        this.currentKafkaReceiver.set(kafkaReceiver);
        this.resourceIdFlowReader = resourceIdFlowReader;
        this.ipClassificationLookup = ipClassificationLookup;
        this.ipClassificationFlowWriter = ipClassificationFlowWriter;
        this.sinkTopic = getSinkTopic(ipClassificationMmdbConfig);
        this.ipClassificationMmdbConfig = ipClassificationMmdbConfig;
        this.ipClassificationScheduler = ipClassificationScheduler;
        this.kafkaSenders = kafkaSenders;
        this.metricsUtil = metricsUtil;
    }

    /**
     * Fail early if sink topic is not defined.
     *
     * @param ipClassificationMmdbConfig
     * @return
     */
    private String getSinkTopic(IpClassificationMmdbConfig ipClassificationMmdbConfig) {
        return Optional.of(ipClassificationMmdbConfig)
                .map(IpClassificationMmdbConfig::getKafkaSenderConfig)
                .map(IpClassificationMmdbConfig.KafkaSenderConfig::getSinkTopic)
                .orElseThrow(
                        () ->
                                new NoSuchElementException(
                                        "No flows topic configured. Please make sure "
                                                + "ipClassificationMmdb.kafkaSenderConfig.sinkTopic is set."));
    }

    private KafkaSender<String, String> getNextSender() {
        return kafkaSenders.get(
                currentSenderIndex.getAndUpdate(current ->
                        (current + 1) % kafkaSenders.size())
        );
    }

    /**
     * Creates a new Kafka receiver with the same configuration
     */
    private KafkaReceiver<String, String> createKafkaReceiver(IpClassificationMmdbConfig config) {
        // Recreate receiver options using the same configuration
        ReceiverOptions<String, String> receiverOptions = ReceiverOptions
                .<String, String>create(config.createConsumerProps())
                .subscription(java.util.Collections.singleton(config.getKafkaReceiverConfig().getTopic()));

        return KafkaReceiver.create(receiverOptions);
    }

    /**
     * Recreates the Kafka receiver and restarts the pipeline with a specific reason tag
     * Uses synchronization to prevent multiple simultaneous recreations
     */
    private void recreateConsumerAndRestart(String reason) {
        // Check if recreation is already in progress
        if (!isRecreating.compareAndSet(false, true)) {
            log.info("Consumer recreation already in progress, skipping duplicate request");
            return;
        }

        // Add metrics for consumer recreation with reason tag
        Attributes recreationAttributes = Attributes.of(
            AttributeKey.stringKey("reason"), reason
        );
        metricsUtil.incrementConsumerRecreationEvent(recreationAttributes);

        try {
            log.warn("Recreating Kafka consumer due to {} issues...", reason);

            // Stop all monitoring first to prevent additional recreation attempts
            stopMonitoring();

            // Dispose current subscription if exists
            if (disposable != null && !disposable.isDisposed()) {
                disposable.dispose();
                log.info("Disposed current consumer subscription");

                // Give some time for the old consumer to fully disconnect
                Thread.sleep(2000);
            }

            // Get the old receiver and dispose it properly
            KafkaReceiver<String, String> oldReceiver = currentKafkaReceiver.get();
            if (oldReceiver != null) {
                log.info("Disposing old Kafka receiver...");
                // Note: KafkaReceiver doesn't have a direct dispose method,
                // but disposing the subscription should clean it up
            }

            // Create new receiver
            KafkaReceiver<String, String> newReceiver = kafkaReceiverFactory.get();
            currentKafkaReceiver.getAndSet(newReceiver);

            log.info("Created new Kafka receiver, restarting pipeline...");

            // Reset timestamps for new consumer
            lastMessageTime.set(System.currentTimeMillis());
            lastNetworkErrorTime.set(0L);

            // Restart with new receiver - add error handling for the restart
            this.disposable = startInternal()
                    .doOnError(error -> {
                        log.error("Error during pipeline restart: {}", error.getMessage(), error);
                        if (isTimeoutOrNetworkError(error)) {
                            log.warn("Restart failed due to timeout/network error, will retry recreation in 5 seconds");
                            // Reset recreation flag before retry
                            isRecreating.set(false);
                            Mono.delay(Duration.ofSeconds(5))
                                    .doOnNext(tick -> recreateConsumerAndRestart("restart_failure_retry"))
                                    .subscribe();
                        } else {
                            // Reset recreation flag for non-timeout errors too
                            isRecreating.set(false);
                        }
                    })
                    .subscribe(
                            result -> log.debug("Pipeline restart successful: {}", result),
                            error -> {
                                log.error("Pipeline restart failed: {}", error.getMessage(), error);
                                isRecreating.set(false); // Reset flag on error
                            },
                            () -> {
                                log.info("Pipeline restart completed");
                                isRecreating.set(false); // Reset flag on completion
                            }
                    );

            log.info("Pipeline restarted with new consumer");

            // Restart monitoring after successful recreation
            startNetworkMonitor();

        } catch (Exception e) {
            log.error("Failed to recreate consumer: {}", e.getMessage(), e);
            isRecreating.set(false); // Reset flag on exception

            // Retry recreation after delay
            Mono.delay(Duration.ofSeconds(5))
                    .doOnNext(tick -> {
                        log.info("Retrying consumer recreation after failure...");
                        recreateConsumerAndRestart("exception_retry");
                    })
                    .subscribe();
        }
    }

    /**
     * Stops all monitoring to prevent multiple recreation attempts
     */
    private void stopMonitoring() {
        if (networkMonitorDisposable != null && !networkMonitorDisposable.isDisposed()) {
            networkMonitorDisposable.dispose();
            log.info("Stopped network monitor during recreation");
        }
    }

    /**
     * Checks if the error is a timeout or network-related issue that should trigger consumer recreation
     */
    private boolean isTimeoutOrNetworkError(Throwable error) {
        return error instanceof TimeoutException ||
               error instanceof DisconnectException ||
               error instanceof NetworkException ||
               error instanceof RetriableCommitFailedException ||
               error instanceof RetriableException ||
               error instanceof java.util.concurrent.TimeoutException ||
               error instanceof java.net.SocketTimeoutException ||
               error instanceof java.io.IOException ||
               (error.getCause() != null && isTimeoutOrNetworkError(error.getCause())) ||
               error.getMessage() != null && (
                   error.getMessage().toLowerCase().contains("timeout") ||
                   error.getMessage().toLowerCase().contains("network") ||
                   error.getMessage().toLowerCase().contains("connection") ||
                   error.getMessage().toLowerCase().contains("disconnect") ||
                   error.getMessage().toLowerCase().contains("commit failed") ||
                   error.getMessage().toLowerCase().contains("retriable") ||
                   error.getMessage().toLowerCase().contains("consumer is being closed") ||
                   error.getMessage().toLowerCase().contains("consumer coordinator")
               );
    }

    /**
     * Checks if the error indicates the consumer has left the group or is being closed
     */
    private boolean isConsumerLeaveGroupError(Throwable error) {
        if (error == null) return false;

        String message = error.getMessage();
        if (message == null) return false;

        String lowerMessage = message.toLowerCase();
        return lowerMessage.contains("leavegroup") ||
               lowerMessage.contains("leave group") ||
               lowerMessage.contains("consumer is being closed") ||
               lowerMessage.contains("sending leavegroup request") ||
               lowerMessage.contains("member") && lowerMessage.contains("leaving") ||
               (error.getCause() != null && isConsumerLeaveGroupError(error.getCause()));
    }

    public void start() {
        this.disposable = startInternal().subscribe();
        startNetworkMonitor();
    }

    /**
     * Starts a network monitor that detects Kafka network disconnections
     * and proactively recreates the consumer
     */
    private void startNetworkMonitor() {
        // Monitor for network disconnections every 30 seconds
        long monitorIntervalMs = this.ipClassificationMmdbConfig.getKafkaReceiverConfig().getMonitorIntervalInMs();

        this.networkMonitorDisposable = Flux.interval(Duration.ofMillis(monitorIntervalMs))
                .doOnNext(tick -> {
                    long currentTime = System.currentTimeMillis();
                    long lastNetworkError = lastNetworkErrorTime.get();

                    // Check if we've had recent network errors and no messages
                    if (lastNetworkError > 0 && (currentTime - lastNetworkError) < this.ipClassificationMmdbConfig.getKafkaReceiverConfig().getMonitorTimeoutInMs()) { // Within last 180 seconds
                        long lastMessage = lastMessageTime.get();
                        long timeSinceLastMessage = currentTime - lastMessage;

                        if (timeSinceLastMessage > this.ipClassificationMmdbConfig.getKafkaReceiverConfig().getNoMessagesInMs() && !isRecreating.get()) { // No messages for 120 seconds after network error
                            log.warn("Network disconnection detected with no message recovery for {} ms", timeSinceLastMessage);
                            log.warn("Proactively recreating consumer due to network disconnection...");

                            // Reset network error time to avoid multiple recreations
                            lastNetworkErrorTime.set(0L);

                            recreateConsumerAndRestart("network_monitor_disconnection");
                        }
                    }
                })
                .subscribe(
                    tick -> log.trace("Network monitor tick: {}", tick),
                    error -> log.error("Network monitor error: {}", error.getMessage(), error),
                    () -> log.info("Network monitor completed")
                );

        log.info("Started network disconnection monitor (interval: {}ms)", monitorIntervalMs);
    }

    /**
     * Call this method when network errors are detected to trigger proactive monitoring
     */
    public void reportNetworkError() {
        lastNetworkErrorTime.set(System.currentTimeMillis());
        log.debug("Network error reported at {}", System.currentTimeMillis());
    }

    /**
     * Call this method when LeaveGroup scenarios are detected to trigger immediate consumer recreation
     */
    public void reportConsumerLeaveGroup() {
        log.warn("Consumer LeaveGroup scenario detected, triggering immediate recreation");
        if (!isRecreating.get()) {
            // Schedule immediate recreation for LeaveGroup scenarios
            Mono.delay(Duration.ofMillis(100))
                    .doOnNext(tick -> recreateConsumerAndRestart("leave_group_detected"))
                    .subscribe();
        } else {
            log.info("Consumer recreation already in progress, skipping LeaveGroup recreation request");
        }
    }

    public Flux<SenderResult<String>> startInternal() {
        KafkaReceiver<String, String> receiver = currentKafkaReceiver.get();

        Flux<ConsumerRecord<String, String>> receiverFlux =
                receiver
                        .receiveAutoAck()
                        .publishOn(ipClassificationScheduler)
                        .timeout(Duration.ofSeconds(this.ipClassificationMmdbConfig.getKafkaReceiverConfig().getMaxPollIntervalMs() / 1000)) // Add timeout to detect stuck receiver
                        .doOnError(error -> {
                            log.error("Error in Kafka receiver (receiveAutoAck): {}", error.getMessage(), error);
                            if (isConsumerLeaveGroupError(error)) {
                                log.warn("Consumer LeaveGroup detected, will recreate consumer immediately: {}",
                                        error.getMessage());
                                // Report LeaveGroup scenario for immediate handling
                                reportConsumerLeaveGroup();
                            } else if (isTimeoutOrNetworkError(error) || error instanceof java.util.concurrent.TimeoutException) {
                                log.warn("Receiver-level timeout/network error detected, will recreate consumer: {}",
                                        error.getMessage());
                                // Report network error for monitoring
                                reportNetworkError();
                            }
                        })
                        .onErrorResume(error -> {
                            if (isConsumerLeaveGroupError(error)) {
                                log.warn("Handling LeaveGroup error by immediately recreating consumer: {}", error.getMessage());
                                // Report LeaveGroup scenario for immediate handling
                                reportConsumerLeaveGroup();
                                // Return empty flux to terminate current stream gracefully
                                return Flux.empty();
                            } else if (isTimeoutOrNetworkError(error) || error instanceof java.util.concurrent.TimeoutException) {
                                log.warn("Handling receiver-level error by recreating consumer: {}", error.getMessage());
                                // Schedule consumer recreation asynchronously
                                Mono.delay(Duration.ofSeconds(1))
                                        .doOnNext(tick -> recreateConsumerAndRestart("receiver_timeout_network_error"))
                                        .subscribe();
                                // Return empty flux to terminate current stream gracefully
                                return Flux.empty();
                            }
                            // For non-timeout errors, propagate the error
                            return Flux.error(error);
                        })
                        .flatMap(records -> records)
                        .doOnNext(record -> {
                            // Update last message time for health check
                            lastMessageTime.set(System.currentTimeMillis());
                            log.trace("Received message, updated health check timestamp");
                        });

        // Apply backpressure configuration if enabled
        if (ipClassificationMmdbConfig.getBackpressureConfig().isEnabled()) {
            receiverFlux =
                    receiverFlux
                            .limitRate(
                                    ipClassificationMmdbConfig
                                            .getBackpressureConfig()
                                            .getHighTide(),
                                    ipClassificationMmdbConfig.getBackpressureConfig().getLowTide())
                            .bufferTimeout(
                                    ipClassificationMmdbConfig.getBackpressureConfig().getMaxSize(),
                                    ipClassificationMmdbConfig
                                            .getBackpressureConfig()
                                            .getMaxTime())
                            .flatMap(Flux::fromIterable);
        }

        return getNextSender()
                .send(
                        receiverFlux
                                .doOnSubscribe(__ -> log.info("IpClassificationMmdbPipeline started for receiver."))
                                .doOnComplete(() -> log.info("IpClassificationMmdbPipeline completed normally."))
                                .doOnCancel(() -> log.warn("IpClassificationMmdbPipeline was cancelled unexpectedly."))
                                .retryWhen(
                                        Retry.backoff(
                                                        ipClassificationMmdbConfig
                                                                .getKafkaReceiverConfig()
                                                                .getMaxRetries(),
                                                        ipClassificationMmdbConfig
                                                                .getKafkaReceiverConfig()
                                                                .getMaxBackoff())
                                                .filter(throwable -> {
                                                    // For LeaveGroup errors, recreate consumer immediately instead of retrying
                                                    if (isConsumerLeaveGroupError(throwable)) {
                                                        log.warn("LeaveGroup error detected, recreating consumer immediately instead of retrying: {}",
                                                                throwable.getMessage());
                                                        // Report LeaveGroup scenario for immediate handling
                                                        reportConsumerLeaveGroup();
                                                        return false; // Don't retry, we're recreating
                                                    }
                                                    // For timeout/network errors, recreate consumer instead of retrying
                                                    else if (isTimeoutOrNetworkError(throwable)) {
                                                        log.warn("Timeout/network error detected, recreating consumer instead of retrying: {}",
                                                                throwable.getMessage());
                                                        // Schedule consumer recreation asynchronously
                                                        Mono.delay(Duration.ofSeconds(2))
                                                                .doOnNext(tick -> recreateConsumerAndRestart("retry_filter_timeout_network"))
                                                                .subscribe();
                                                        return false; // Don't retry, we're recreating
                                                    }
                                                    return true; // Allow retry for other errors
                                                })
                                                .doBeforeRetry(
                                                        retrySignal ->
                                                                log.warn(
                                                                        "Error receiving from Kafka, retrying... {} (attempt {})",
                                                                        retrySignal.failure().getMessage(),
                                                                        retrySignal.totalRetries() + 1)))
                                .flatMap(this::processConsumerRecord))
                        .doOnError(error -> {
                            log.error("Error in sender pipeline: {}", error.getMessage(), error);
                            if (isConsumerLeaveGroupError(error)) {
                                log.warn("Sender-level LeaveGroup error, recreating consumer immediately: {}", error.getMessage());
                                reportConsumerLeaveGroup();
                            } else if (isTimeoutOrNetworkError(error)) {
                                log.warn("Sender-level timeout/network error detected: {}", error.getMessage());
                                // Note: Not recreating consumer for sender errors
                            }
                        })
                        .onErrorResume(error -> {
                            if (isConsumerLeaveGroupError(error)) {
                                log.warn("Handling sender-level LeaveGroup error by immediately recreating consumer: {}", error.getMessage());
                                reportConsumerLeaveGroup();
                                return Flux.empty(); // Terminate current stream gracefully
                            } else if (isTimeoutOrNetworkError(error)) {
                                log.warn("Handling sender-level timeout/network error: {}", error.getMessage());
                                // Note: Not recreating consumer for sender errors, letting retry mechanism handle it
                                return Flux.error(error); // Let retry mechanism handle sender errors
                            }
                            return Flux.error(error); // Propagate non-timeout errors
                        })
                        .retryWhen(
                                Retry.backoff(
                                        ipClassificationMmdbConfig
                                                .getKafkaSenderConfig()
                                                .getMaxRetries(),
                                        ipClassificationMmdbConfig
                                                .getKafkaSenderConfig()
                                                .getMaxBackoff())
                                        .filter(throwable -> !isTimeoutOrNetworkError(throwable) && !isConsumerLeaveGroupError(throwable)) // Don't retry timeout or LeaveGroup errors
                                        .doBeforeRetry(
                                                retrySignal ->
                                                        log.warn(
                                                                "Error sending to Kafka, retrying... {}",
                                                                retrySignal)));
    }

    public Mono<SenderRecord<String, String, String>> processConsumerRecord(
            ConsumerRecord<String, String> consumerRecord) {
        return Mono.just(consumerRecord)
                .doOnNext(__ -> log.debug("Processing record {}", consumerRecord))
                .flatMap(
                        __ ->
                                resourceIdFlowReader
                                        .readTree(consumerRecord.value())
                                        .doOnError(
                                                throwable ->
                                                        log.debug(
                                                                "Error parsing record {}",
                                                                consumerRecordString(
                                                                        consumerRecord),
                                                                throwable))
                                        .flatMap(
                                                node ->
                                                        ipClassificationLookup
                                                                .maybeAddIpClassification(node)
                                                                .doOnError(
                                                                        throwable ->
                                                                                log.debug(
                                                                                        "IP Classification Lookup failed: [{}] for record key [{}]",
                                                                                        throwable,
                                                                                        consumerRecord
                                                                                                .key()))
                                                                .doOnNext(enrichedRecord -> {
                                                                    // Extract tenant ID for outgoing metrics
                                                                    String tenantId = FlowDataValidator.extractTenantId(enrichedRecord);
                                                                    Attributes tenantAttributes = Attributes.builder()
                                                                            .put(FlowDataValidator.INSIGHTS_TENANT_ID, tenantId)
                                                                            .build();
                                                                    metricsUtil.incrementIPClassificationOutgoingEvent(tenantAttributes);
                                                                }))
                                        .flatMap(ipClassificationFlowWriter::writeTreeAsString))
                .doOnError(
                        throwable -> {
                                log.warn(
                                        "Error processing record: {}",
                                        throwable
                                                .getClass()
                                                .getSimpleName()); // if it reaches here, it seems
                        })
                .onErrorReturn(consumerRecord.value())
                .flatMap(
                        newValue ->
                        {
                                return Mono.just(
                                        SenderRecord.create(
                                                new ProducerRecord<>(
                                                        sinkTopic, consumerRecord.key(), newValue),
                                                consumerRecordString(consumerRecord)));
                        }
                );
    }

    private String consumerRecordString(ConsumerRecord<String, String> consumerRecord) {
        return consumerRecord.topic()
                + "-"
                + consumerRecord.partition()
                + "@"
                + consumerRecord.offset();
    }



    public void stop() {
        log.info("Stopping IpClassificationMmdbPipeline...");

        // Set recreation flag to prevent any new recreation attempts
        isRecreating.set(true);

        // Stop all monitoring
        stopMonitoring();

        // Stop main pipeline
        if (this.disposable != null && !this.disposable.isDisposed()) {
            this.disposable.dispose();
            log.info("Stopped main pipeline");
        }

        log.info("IpClassificationMmdbPipeline stopped completely");
    }
}
