package com.illumio.data.repository;

import com.illumio.data.model.DerivedMetadata;
import com.illumio.data.model.Pagination;
import com.illumio.data.model.RequestContext;
import com.illumio.data.model.RequestPayload;
import com.illumio.data.model.TimeFrame;
import com.illumio.data.model.constants.Fields;
import com.illumio.data.model.constants.Metadata;
import com.illumio.data.model.constants.TableType;
import com.illumio.data.model.constants.WidgetId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static com.illumio.data.model.constants.Fields.AGGREGATED_BYTES;
import static com.illumio.data.model.constants.Fields.AGGREGATED_FLOWS;
import static com.illumio.data.model.constants.Fields.DESTINATION_EXTERNAL_LABEL;
import static com.illumio.data.model.constants.Fields.DESTINATION_EXTERNAL_LABEL_CATEGORY;
import static com.illumio.data.model.constants.Fields.PORT;
import static com.illumio.data.model.constants.Fields.PROTOCOL;
import static com.illumio.data.model.constants.Fields.SOURCE_RESOURCE_CATEGORY;
import static com.illumio.data.model.constants.Fields.SOURCE_RESOURCE_ID;
import static com.illumio.data.model.constants.Fields.SOURCE_RESOURCE_NAME;
import static com.illumio.data.model.constants.Fields.SOURCE_RESOURCE_TYPE;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class KustoQueryRepositoryExternalTransferTest {
    private KustoQueryRepository repository;
    private RequestPayload payload;
    private final String tenantId = "testTenant";
    private RequestContext requestContext;


    @BeforeEach
    void setUp() {
        repository = new KustoQueryRepository();

        TimeFrame currentTimeFrame = TimeFrame.builder().startTime("2025-01-01T00:00:00Z").endTime("2025-01-02T00:00:00Z").build();
        TimeFrame comparisonTimeFrame = TimeFrame.builder().startTime("2025-01-01T00:00:00Z").endTime("2025-01-02T00:00:00Z").build();

        Pagination pagination = Pagination.builder().build();
        pagination.setPageNumber(1);
        pagination.setRowLimit(10);

        payload = RequestPayload.builder().build();
        payload.setCurrentTimeFrame(currentTimeFrame);
        payload.setComparisonTimeFrame(comparisonTimeFrame);
        payload.setPagination(pagination);
        payload.setFilters(Collections.emptyList());
        payload.setSortByFields(Collections.emptyList());

        DerivedMetadata derivedMetadata = new DerivedMetadata(
                "Insights_ExfiltrationTraffic_Hourly",
                "Insights_ExfiltrationTraffic_Hourly",
                currentTimeFrame,
                comparisonTimeFrame,
                TableType.AGGREGATED_TABLE,
                TableType.AGGREGATED_TABLE);
        requestContext = RequestContext.builder()
                .tenantId(Optional.of(tenantId))
                .requestPayload(Optional.of(payload))
                .derivedMetadata(Optional.of(derivedMetadata))
                .build();
    }

    @Test
    void testGetQueryString_ExternalTransferByCategory() {
        Metadata metadata = Metadata.builder()
                .widgetId(WidgetId.EXTERNAL_DESTINATION_CATEGORY)
                .groupByFields(List.of(DESTINATION_EXTERNAL_LABEL_CATEGORY, DESTINATION_EXTERNAL_LABEL))
                .summarizeFields(List.of(AGGREGATED_FLOWS, AGGREGATED_BYTES))
                .build();
        String query = repository.getQueryString(requestContext, metadata);
        assertNotNull(query);

        assertTrue(query.contains("ExfiltrationTraffic"), "Query should contain exfiltration traffic database name");
        assertTrue(query.contains("let curr = "), "Query should contain current timeframe data");
        assertTrue(query.contains("let prev = "), "Query should contain previous timeframe data");
        assertTrue(query.contains("curr | join kind = leftouter(prev)"), "Query should join curr and prev data");
        assertTrue(query.contains("|sort by"), "Query should include sorting logic");
        assertTrue(query.contains("let totalRows = toscalar(rows | count);"), "Query should include pagination logic");
    }

    @Test
    void testGetQueryString_ExternalTransferService() {
        Metadata metadata = Metadata.builder()
                .widgetId(WidgetId.EXTERNAL_SERVICE_TRANSFER)
                .groupByFields(List.of(PORT, PROTOCOL))
                .summarizeFields(List.of(AGGREGATED_FLOWS, AGGREGATED_BYTES))
                .build();
        String query = repository.getQueryString(requestContext, metadata);
        assertNotNull(query);

        assertTrue(query.contains("ExfiltrationTraffic"), "Query should contain exfiltration traffic database name");
        assertTrue(query.contains("let curr = "), "Query should contain current timeframe data");
        assertTrue(query.contains("let prev = "), "Query should contain previous timeframe data");
        assertTrue(query.contains("curr | join kind = leftouter(prev)"), "Query should join curr and prev data");
        assertTrue(query.contains("|sort by"), "Query should include sorting logic");
    }

    @Test
    void testGetQueryString_TopSourceRoleTransfer() {
        Metadata metadata = Metadata.builder()
                .widgetId(WidgetId.TOP_SOURCE_ROLE_TRANSFER)
                .build();
        String query = repository.getQueryString(requestContext, metadata);
        assertNotNull(query);

        assertTrue(query.contains("ExfiltrationTraffic"), "Query should contain exfiltration traffic database name");
        assertTrue(query.contains("let curr = "), "Query should contain current timeframe data");
        assertTrue(query.contains("let prev = "), "Query should contain previous timeframe data");
        assertTrue(query.contains("TotalFlowCount"), "Query should contain total flow count data");
        assertTrue(query.contains("TotalByteCount"), "Query should contain total byte count data");
        assertTrue(query.contains("let prev = "), "Query should contain previous timeframe data");
        assertTrue(query.contains("join kind=leftouter(prevFlows)"), "Query should join on previous flows data");
        assertTrue(query.contains("join kind=leftouter(prevBytes)"), "Query should join on previous bytes data");
        assertTrue(query.contains("project SourceLabel"), "Query should project source label");
        assertTrue(query.contains("union Flows, Bytes;"), "Query should include union query");
    }

    @Test
    void testGetQueryString_ExternalGeoTransfer() {
        Metadata metadata = Metadata.builder()
                .widgetId(WidgetId.EXTERNAL_GEO_TRANSFER)
                .build();
        String query = repository.getQueryString(requestContext, metadata);
        assertNotNull(query);

        assertTrue(query.contains("ExfiltrationTraffic"), "Query should contain exfiltration traffic database name");
        assertTrue(query.contains("let curr = "), "Query should contain current timeframe data");
        assertTrue(query.contains("let prev = "), "Query should contain previous timeframe data");
        assertTrue(query.contains("TotalFlowCount"), "Query should contain total flow count data");
        assertTrue(query.contains("TotalByteCount"), "Query should contain total byte count data");
        assertTrue(query.contains("let prev = "), "Query should contain previous timeframe data");
        assertTrue(query.contains("join kind=leftouter(prevFlows)"), "Query should join on previous flows data");
        assertTrue(query.contains("join kind=leftouter(prevBytes)"), "Query should join on previous bytes data");
        assertTrue(query.contains("project DestCountry"), "Query should project destination country");
        assertTrue(query.contains("union Flows, Bytes;"), "Query should include union query");
    }

    @Test
    void testGetQueryString_TopSourceTransfer() {
        Metadata metadata = Metadata.builder()
                .widgetId(WidgetId.TOP_SOURCE_TRANSFER)
                .groupByFields(List.of(SOURCE_RESOURCE_ID, SOURCE_RESOURCE_NAME, SOURCE_RESOURCE_CATEGORY, SOURCE_RESOURCE_TYPE))
                .tableName("ExfiltrationTraffic")
                .build();
        String query = repository.getQueryString(requestContext, metadata);
        assertNotNull(query);

        assertTrue(query.contains("ExfiltrationTraffic"), "Query should contain exfiltration traffic database name");
        assertTrue(query.contains("let topSrcRes = "), "Query should contain main selection data");
        assertTrue(query.contains("TotalFlowCount"), "Query should contain total flow count data");
        assertTrue(query.contains("TotalByteCount"), "Query should contain total byte count data");
        assertTrue(query.contains("FlowsTimeSeries"), "Query should contain FlowsTimeSeries data");
        assertTrue(query.contains("BytesTimeSeries"), "Query should contain BytesTimeSeries data");
        assertTrue(query.contains(" TimeSeries = bin("+ Fields.START_TIME.getTableColumnName()+", 1h)"), "Query should create a timeseries on start time");
        assertTrue(query.contains("union FlowsTimeSeries, BytesTimeSeries;"), "Query should include union query");
    }

    @Test
    void testGetQueryString_ThirdPartyOutbound() {
        Metadata metadata = Metadata.builder()
                .widgetId(WidgetId.THIRD_PARTY_DEPENDENCY_OUTBOUND)
                .groupByFields(List.of(DESTINATION_EXTERNAL_LABEL_CATEGORY, DESTINATION_EXTERNAL_LABEL))
                .summarizeFields(List.of(AGGREGATED_FLOWS, AGGREGATED_BYTES))
                .build();
        String query = repository.getQueryString(requestContext, metadata);
        assertNotNull(query);
        assertTrue(query.contains("Insights_ExfiltrationTraffic_Hourly"), "Query should contain the base table");
        assertTrue(query.contains(String.format("| where IllumioTenantId == '%s'", tenantId)), "Query should contain tenant condition");
        assertTrue(query.contains("| summarize"), "Query should include grouping logic");
        assertTrue(query.contains("|sort by"), "Query should include sorting logic");
        assertTrue(query.contains("let curr = "), "Query should include current and previous logic");
        assertTrue(query.contains("let prev = "), "Query should include current and previous logic");
        assertTrue(query.contains("let totalRows = toscalar(rows | count);"), "Query should include pagination logic");
    }

}
