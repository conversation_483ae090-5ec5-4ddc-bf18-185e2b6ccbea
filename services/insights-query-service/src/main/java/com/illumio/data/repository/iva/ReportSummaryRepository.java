package com.illumio.data.repository.iva;

import com.illumio.data.model.ReportSummaryEntity;

import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

import java.util.UUID;

@Repository
public interface ReportSummaryRepository extends ReactiveCrudRepository<ReportSummaryEntity, Integer> {

    /**
     * Find the most recent automatic report for a tenant and persona
     * Only returns reports generated by cron jobs (report_generate_method = 'automatic')
     * Returns single report as per API specification (no pagination)
     */
    @Query("SELECT * FROM report_summary WHERE tenant_id = :tenantId " +
           "AND persona = :persona " +
           "AND report_generate_method = 'automatic' " +
           "ORDER BY created_at DESC " +
           "LIMIT 1")
    Mono<ReportSummaryEntity> findMostRecentAutomaticReportByTenantAndPersona(
            UUID tenantId,
            String persona);

    /**
     * Find a specific report by ID and tenant for security
     */
    Mono<ReportSummaryEntity> findByReportIdAndTenantId(Integer reportId, UUID tenantId);
}
