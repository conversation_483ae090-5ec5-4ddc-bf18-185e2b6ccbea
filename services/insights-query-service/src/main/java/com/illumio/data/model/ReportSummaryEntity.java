package com.illumio.data.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

import java.time.LocalDateTime;
import java.util.UUID;

@Table("report_summary")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ReportSummaryEntity {
    
    @Id
    @Column("report_id")
    private Integer reportId;
    
    @Column("comparison_start_time")
    private LocalDateTime comparisonStartTime;
    
    @Column("comparison_end_time")
    private LocalDateTime comparisonEndTime;
    
    @Column("current_start_time")
    private LocalDateTime currentStartTime;
    
    @Column("current_end_time")
    private LocalDateTime currentEndTime;
    
    @Column("created_at")
    private LocalDateTime createdAt;
    
    @Column("persona")
    private String persona;
    
    @Column("report_generate_method")
    private String reportGenerateMethod;
    
    @Column("report_payload")
    private String reportPayload; // JSONB stored as String
    
    @Column("tenant_id")
    private UUID tenantId;
}
