package com.illumio.data.filter;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.OrderedGatewayFilter;
import org.springframework.cloud.gateway.filter.RouteToRequestUrlFilter;
import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;
import org.springframework.stereotype.Component;

import static org.springframework.cloud.gateway.support.ServerWebExchangeUtils.GATEWAY_REQUEST_URL_ATTR;

@Slf4j
@Component
public class CoreHeaderFilter extends AbstractGatewayFilterFactory<CoreHeaderFilter.Config> {

    public CoreHeaderFilter() {
        super(Config.class); // <-- REQUIRED so SCG binds args into Config
    }
    @Getter
    @Setter
    public static class Config {
        private String message;
    }

    @Override
    public GatewayFilter apply(Config config) {
        return new OrderedGatewayFilter((exchange, chain) -> {
          String pce_fqdn = exchange.getRequest().getHeaders().getFirst("X-PCE-FQDN");
          exchange.getAttributes().put(GATEWAY_REQUEST_URL_ATTR, pce_fqdn);
          return chain.filter(exchange.mutate().request(exchange.getRequest()).build());

        }, RouteToRequestUrlFilter.ROUTE_TO_URL_FILTER_ORDER+1);

    }

}
